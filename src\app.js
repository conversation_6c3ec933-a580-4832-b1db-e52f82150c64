require('dotenv').config();
const express = require('express');
const cors = require('cors');
const path = require('path');
const { createServer } = require('http');
const TradingBot = require('./core/TradingBot');
const WebSocketManager = require('./websocket/WebSocketManager');
const apiRoutes = require('./routes/api');
const { setupStaticFiles } = require('./utils/fileSetup');

class App {
    constructor() {
        this.app = express();
        this.server = createServer(this.app);
        this.port = process.env.PORT || 3000;
        
        this.setupMiddleware();
        this.setupRoutes();
        this.initializeServices();
        this.setupGracefulShutdown();
    }
    
    setupMiddleware() {
        this.app.use(cors());
        this.app.use(express.json());
        this.app.use(express.static(path.join(__dirname, '../public')));
    }
    
    setupRoutes() {
        this.app.use('/api', apiRoutes);
        
        // Serve dashboard
        this.app.get('/', (req, res) => {
            res.sendFile(path.join(__dirname, '../public/index.html'));
        });
    }
    
    async initializeServices() {
        try {
            // Setup static files
            await setupStaticFiles();
            
            // Initialize trading bot
            this.tradingBot = new TradingBot();
            
            // Initialize WebSocket manager
            this.wsManager = new WebSocketManager(this.server, this.tradingBot);
            
            // Pass WebSocket manager to API routes
            this.app.locals.tradingBot = this.tradingBot;
            this.app.locals.wsManager = this.wsManager;
            
            console.log('✅ All services initialized successfully');
            
        } catch (error) {
            console.error('❌ Failed to initialize services:', error);
            process.exit(1);
        }
    }
    
    setupGracefulShutdown() {
        const shutdown = (signal) => {
            console.log(`\n📡 Received ${signal}, shutting down gracefully...`);
            
            if (this.tradingBot) {
                this.tradingBot.stop();
            }
            
            if (this.wsManager) {
                this.wsManager.close();
            }
            
            this.server.close(() => {
                console.log('✅ Server closed successfully');
                process.exit(0);
            });
        };
        
        process.on('SIGTERM', () => shutdown('SIGTERM'));
        process.on('SIGINT', () => shutdown('SIGINT'));
    }
    
    start() {
        this.server.listen(this.port, () => {
            console.log(`
🚀 SPX Trading Bot Server Started
📊 Dashboard: http://localhost:${this.port}
🔧 API: http://localhost:${this.port}/api
📈 Environment: ${process.env.NODE_ENV || 'development'}
            `);
        });
    }
}

// Start the application
if (require.main === module) {
    const app = new App();
    app.start();
}

module.exports = App;

// src/core/TradingBot.js
const EventEmitter = require('events');
const TechnicalIndicators = require('./TechnicalIndicators');
const MarketDataProvider = require('./MarketDataProvider');
const SignalGenerator = require('./SignalGenerator');

class TradingBot extends EventEmitter {
    constructor() {
        super();
        this.config = {
            symbol: process.env.DEFAULT_SYMBOL || 'SPX',
            timeframes: ['1min', '5min', '15min', '1h', '4h', '1d'],
            currentTimeframe: process.env.DEFAULT_TIMEFRAME || '1d',
            emaLength: 50,
            rsiLength: 14,
            updateInterval: parseInt(process.env.UPDATE_INTERVAL) || 5000
        };
        
        this.marketData = {
            prices: [],
            timestamps: [],
            signals: [],
            indicators: {}
        };
        
        this.isRunning = false;
        this.updateTimer = null;
        
        this.marketDataProvider = new MarketDataProvider();
        this.signalGenerator = new SignalGenerator(this.config);
        
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        this.marketDataProvider.on('dataUpdate', (data) => {
            this.processMarketData(data);
        });
        
        this.marketDataProvider.on('error', (error) => {
            console.error('Market data error:', error);
            this.emit('error', error);
        });
        
        this.signalGenerator.on('signal', (signal) => {
            this.marketData.signals.push(signal);
            this.emit('signal', signal);
            console.log(`📊 New ${signal.type} signal: $${signal.price.toFixed(2)} (${signal.confidence.toFixed(1)}%)`);
        });
    }
    
    processMarketData(data) {
        this.marketData.prices = data.prices;
        this.marketData.timestamps = data.timestamps;
        
        // Calculate technical indicators
        this.marketData.indicators = {
            ema50: TechnicalIndicators.calculateEMA(data.prices, this.config.emaLength),
            rsi: TechnicalIndicators.calculateRSI(data.prices, this.config.rsiLength),
            squeeze: TechnicalIndicators.calculateSqueeze(data.prices),
            bollingerBands: TechnicalIndicators.calculateBollingerBands(data.prices)
        };
        
        // Generate signals
        this.signalGenerator.analyze(this.marketData);
        
        this.emit('dataUpdate', this.marketData);
    }
    
    async start() {
        if (this.isRunning) {
            console.log('⚠️ Bot is already running');
            return;
        }
        
        this.isRunning = true;
        console.log(`🚀 Starting SPX Trading Bot - ${this.config.symbol} ${this.config.currentTimeframe}`);
        
        try {
            // Initial data fetch
            await this.marketDataProvider.fetchData(
                this.config.symbol,
                this.config.currentTimeframe
            );
            
            // Setup periodic updates
            this.updateTimer = setInterval(async () => {
                if (this.isRunning) {
                    try {
                        await this.marketDataProvider.fetchData(
                            this.config.symbol,
                            this.config.currentTimeframe
                        );
                    } catch (error) {
                        console.error('Update error:', error);
                    }
                }
            }, this.config.updateInterval);
            
            this.emit('started');
            console.log('✅ Bot started successfully');
            
        } catch (error) {
            console.error('❌ Failed to start bot:', error);
            this.isRunning = false;
            this.emit('error', error);
        }
    }
    
    stop() {
        if (!this.isRunning) return;
        
        this.isRunning = false;
        
        if (this.updateTimer) {
            clearInterval(this.updateTimer);
            this.updateTimer = null;
        }
        
        this.emit('stopped');
        console.log('⏹️ Trading Bot stopped');
    }
    
    updateConfig(newConfig) {
        const { timeframe, symbol } = newConfig;
        
        if (timeframe && this.config.timeframes.includes(timeframe)) {
            this.config.currentTimeframe = timeframe;
        }
        
        if (symbol) {
            this.config.symbol = symbol;
        }
        
        this.emit('configUpdated', this.config);
        
        // Restart data fetching with new config if running
        if (this.isRunning) {
            this.marketDataProvider.fetchData(
                this.config.symbol,
                this.config.currentTimeframe
            );
        }
    }
    
    getStatus() {
        return {
            running: this.isRunning,
            symbol: this.config.symbol,
            timeframe: this.config.currentTimeframe,
            lastUpdate: new Date().toISOString(),
            totalSignals: this.marketData.signals.length,
            dataPoints: this.marketData.prices.length
        };
    }
    
    getData() {
        return {
            marketData: this.marketData,
            config: this.config
        };
    }
}

module.exports = TradingBot;

// src/core/TechnicalIndicators.js
class TechnicalIndicators {
    static calculateEMA(prices, period) {
        if (!prices || prices.length < period) return [];
        
        const multiplier = 2 / (period + 1);
        const ema = [];
        
        // Start with SMA for the first value
        let sum = 0;
        for (let i = 0; i < period; i++) {
            sum += prices[i];
        }
        ema[period - 1] = sum / period;
        
        // Calculate EMA for subsequent values
        for (let i = period; i < prices.length; i++) {
            ema[i] = (prices[i] * multiplier) + (ema[i - 1] * (1 - multiplier));
        }
        
        return ema;
    }
    
    static calculateRSI(prices, period = 14) {
        if (!prices || prices.length < period + 1) return [];
        
        const rsi = [];
        let gains = 0;
        let losses = 0;
        
        // Calculate initial average gain and loss
        for (let i = 1; i <= period; i++) {
            const change = prices[i] - prices[i - 1];
            if (change >= 0) {
                gains += change;
            } else {
                losses -= change;
            }
        }
        
        let avgGain = gains / period;
        let avgLoss = losses / period;
        let rs = avgGain / avgLoss;
        rsi[period] = 100 - (100 / (1 + rs));
        
        // Calculate subsequent RSI values
        for (let i = period + 1; i < prices.length; i++) {
            const change = prices[i] - prices[i - 1];
            const gain = change >= 0 ? change : 0;
            const loss = change < 0 ? -change : 0;
            
            avgGain = (avgGain * (period - 1) + gain) / period;
            avgLoss = (avgLoss * (period - 1) + loss) / period;
            
            rs = avgGain / avgLoss;
            rsi[i] = 100 - (100 / (1 + rs));
        }
        
        return rsi;
    }
    
    static calculateBollingerBands(prices, period = 20, stdDev = 2) {
        if (!prices || prices.length < period) return { upper: [], middle: [], lower: [] };
        
        const upper = [];
        const middle = [];
        const lower = [];
        
        for (let i = period - 1; i < prices.length; i++) {
            const slice = prices.slice(i - period + 1, i + 1);
            const sma = slice.reduce((a, b) => a + b) / period;
            
            const variance = slice.reduce((sum, price) => {
                return sum + Math.pow(price - sma, 2);
            }, 0) / period;
            
            const standardDeviation = Math.sqrt(variance);
            
            middle[i] = sma;
            upper[i] = sma + (standardDeviation * stdDev);
            lower[i] = sma - (standardDeviation * stdDev);
        }
        
        return { upper, middle, lower };
    }
    
    static calculateSqueeze(prices, period = 20, keltnerPeriod = 20, keltnerMultiplier = 1.5) {
        if (!prices || prices.length < Math.max(period, keltnerPeriod)) return [];
        
        const bb = this.calculateBollingerBands(prices, period);
        const ema = this.calculateEMA(prices, keltnerPeriod);
        
        // Calculate Keltner Channels using simplified ATR
        const keltner = { upper: [], lower: [] };
        
        for (let i = keltnerPeriod - 1; i < prices.length; i++) {
            if (ema[i] !== undefined) {
                const slice = prices.slice(Math.max(0, i - keltnerPeriod + 1), i + 1);
                const high = Math.max(...slice);
                const low = Math.min(...slice);
                const atr = (high - low) / slice.length;
                
                keltner.upper[i] = ema[i] + (atr * keltnerMultiplier);
                keltner.lower[i] = ema[i] - (atr * keltnerMultiplier);
            }
        }
        
        // Determine squeeze state
        const squeeze = [];
        for (let i = 0; i < prices.length; i++) {
            if (bb.upper[i] !== undefined && keltner.upper[i] !== undefined) {
                const bbWidth = bb.upper[i] - bb.lower[i];
                const keltnerWidth = keltner.upper[i] - keltner.lower[i];
                
                if (bbWidth < keltnerWidth * 0.8) {
                    squeeze[i] = 'SQUEEZE'; // Dark Blue - High Compression
                } else if (bbWidth < keltnerWidth * 1.1) {
                    squeeze[i] = 'YELLOW'; // Yellow - Medium Compression
                } else {
                    squeeze[i] = 'GREEN'; // Green - No Squeeze
                }
            }
        }
        
        return squeeze;
    }
    
    static calculateATR(highs, lows, closes, period = 14) {
        if (!highs || !lows || !closes || highs.length < period) return [];
        
        const trueRanges = [];
        
        for (let i = 1; i < highs.length; i++) {
            const highLow = highs[i] - lows[i];
            const highClose = Math.abs(highs[i] - closes[i - 1]);
            const lowClose = Math.abs(lows[i] - closes[i - 1]);
            
            trueRanges.push(Math.max(highLow, highClose, lowClose));
        }
        
        return this.calculateEMA(trueRanges, period);
    }
}

module.exports = TechnicalIndicators;

// src/core/MarketDataProvider.js
const EventEmitter = require('events');
const axios = require('axios');

class MarketDataProvider extends EventEmitter {
    constructor() {
        super();
        this.apiKey = process.env.ALPHA_VANTAGE_API_KEY || 'demo';
        this.lastFetchTime = null;
        this.rateLimitDelay = 12000; // 12 seconds between API calls (5 calls per minute limit)
    }
    
    async fetchData(symbol, timeframe) {
        try {
            // Rate limiting
            if (this.lastFetchTime && Date.now() - this.lastFetchTime < this.rateLimitDelay) {
                console.log('⏳ Rate limiting: using cached data');
                return;
            }
            
            let data;
            
            if (this.apiKey === 'demo') {
                data = this.generateSampleData(symbol, timeframe);
            } else {
                data = await this.fetchRealData(symbol, timeframe);
            }
            
            this.lastFetchTime = Date.now();
            this.emit('dataUpdate', data);
            
        } catch (error) {
            console.error('❌ Data fetch error:', error);
            this.emit('error', error);
            
            // Fallback to sample data
            const fallbackData = this.generateSampleData(symbol, timeframe);
            this.emit('dataUpdate', fallbackData);
        }
    }
    
    async fetchRealData(symbol, timeframe) {
        const timeSeriesMap = {
            '1min': 'TIME_SERIES_INTRADAY&interval=1min',
            '5min': 'TIME_SERIES_INTRADAY&interval=5min',
            '15min': 'TIME_SERIES_INTRADAY&interval=15min',
            '1h': 'TIME_SERIES_INTRADAY&interval=60min',
            '4h': 'TIME_SERIES_INTRADAY&interval=60min',
            '1d': 'TIME_SERIES_DAILY'
        };
        
        const functionName = timeSeriesMap[timeframe] || 'TIME_SERIES_DAILY';
        const url = `https://www.alphavantage.co/query?function=${functionName}&symbol=${symbol}&apikey=${this.apiKey}&outputsize=full`;
        
        console.log(`📡 Fetching real data for ${symbol} ${timeframe}...`);
        
        const response = await axios.get(url, { timeout: 10000 });
        return this.processApiResponse(response.data);
    }
    
    processApiResponse(apiData) {
        const timeSeries = apiData[Object.keys(apiData).find(key => key.includes('Time Series'))];
        
        if (!timeSeries) {
            throw new Error('No time series data found in API response');
        }
        
        const prices = [];
        const timestamps = [];
        
        Object.keys(timeSeries)
            .sort() // Ensure chronological order
            .forEach(timestamp => {
                const candle = timeSeries[timestamp];
                prices.push(parseFloat(candle['4. close']));
                timestamps.push(timestamp);
            });
        
        return { prices, timestamps };
    }
    
    generateSampleData(symbol = 'SPX', timeframe = '1d') {
        const dataPoints = this.getDataPointsForTimeframe(timeframe);
        const basePrice = this.getBasePriceForSymbol(symbol);
        
        const prices = [];
        const timestamps = [];
        
        let currentPrice = basePrice;
        const now = new Date();
        const timeframeMs = this.getTimeframeMs(timeframe);
        
        console.log(`🔄 Generating ${dataPoints} sample data points for ${symbol} ${timeframe}`);
        
        for (let i = dataPoints - 1; i >= 0; i--) {
            // Enhanced random walk with volatility clustering
            const volatility = 0.01 + (Math.random() * 0.02); // 1-3% volatility
            const momentum = (Math.random() - 0.48) * volatility; // Slight upward bias
            
            currentPrice = Math.max(
                basePrice * 0.8,
                Math.min(basePrice * 1.2, currentPrice * (1 + momentum))
            );
            
            prices.push(currentPrice);
            
            const timestamp = new Date(now.getTime() - (i * timeframeMs));
            timestamps.push(timestamp.toISOString());
        }
        
        return { prices, timestamps };
    }
    
    getDataPointsForTimeframe(timeframe) {
        const dataPointsMap = {
            '1min': 200,
            '5min': 200,
            '15min': 200,
            '1h': 168, // 1 week
            '4h': 180, // 30 days
            '1d': 252  // 1 year trading days
        };
        return dataPointsMap[timeframe] || 200;
    }
    
    getBasePriceForSymbol(symbol) {
        const basePrices = {
            'SPX': 4500,
            'SPY': 450,
            'QQQ': 350,
            'AAPL': 180,
            'MSFT': 350,
            'GOOGL': 130
        };
        return basePrices[symbol] || 100;
    }
    
    getTimeframeMs(timeframe) {
        const timeframeMap = {
            '1min': 60 * 1000,
            '5min': 5 * 60 * 1000,
            '15min': 15 * 60 * 1000,
            '1h': 60 * 60 * 1000,
            '4h': 4 * 60 * 60 * 1000,
            '1d': 24 * 60 * 60 * 1000
        };
        return timeframeMap[timeframe] || timeframeMap['1d'];
    }
}

module.exports = MarketDataProvider;