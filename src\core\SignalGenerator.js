// src/core/SignalGenerator.js
const EventEmitter = require('events');

class SignalGenerator extends EventEmitter {
    constructor(config) {
        super();
        this.config = config;
        this.lastSignal = null;
        this.signalBuffer = [];
        this.minSignalDistance = 5; // Minimum bars between signals
    }
    
    analyze(marketData) {
        const { prices, timestamps, indicators } = marketData;
        const { ema50, rsi, squeeze } = indicators;
        
        if (!prices || prices.length < 50) return; // Not enough data
        
        const currentIndex = prices.length - 1;
        const signal = this.evaluateSignal(
            prices[currentIndex],
            ema50[currentIndex],
            rsi[currentIndex],
            squeeze[currentIndex],
            timestamps[currentIndex],
            currentIndex
        );
        
        if (signal && this.shouldEmitSignal(signal, currentIndex)) {
            this.lastSignal = signal;
            this.emit('signal', signal);
        }
    }
    
    evaluateSignal(currentPrice, ema, currentRsi, currentSqueeze, timestamp, index) {
        if (!ema || !currentRsi || !currentSqueeze) return null;
        
        // Buy Signal Conditions
        const priceAboveEma = currentPrice > (ema * 1.01); // 1% above EMA
        const squeezeYellow = currentSqueeze === 'YELLOW';
        const rsiAbove50 = currentRsi > 50;
        
        // Sell Signal Conditions  
        const priceBelowEma = currentPrice < (ema * 0.99); // 1% below EMA
        const squeezeDarkBlue = currentSqueeze === 'SQUEEZE';
        const rsiBelow50 = currentRsi < 50;
        
        // Additional confirmation signals
        const strongBuyConditions = [
            currentRsi > 60, // Strong RSI
            currentPrice > (ema * 1.02), // 2% above EMA
            this.checkMomentum(index, 'bullish')
        ];
        
        const strongSellConditions = [
            currentRsi < 40, // Weak RSI
            currentPrice < (ema * 0.98), // 2% below EMA
            this.checkMomentum(index, 'bearish')
        ];
        
        let signal = null;
        let confidence = 0;
        let signalStrength = 'NORMAL';
        
        if (priceAboveEma && squeezeYellow && rsiAbove50) {
            signal = {
                type: 'BUY',
                timestamp,
                index,
                price: currentPrice,
                ema,
                rsi: currentRsi,
                squeeze: currentSqueeze,
                conditions: {
                    priceVsEma: ((currentPrice - ema) / ema * 100).toFixed(2) + '%',
                    rsi: currentRsi.toFixed(1),
                    squeeze: currentSqueeze,
                    momentum: this.getMomentumDescription(index)
                }
            };
            
            // Calculate confidence
            const baseFactors = [
                (currentPrice - ema) / ema * 100, // Price premium
                currentRsi - 50, // RSI strength
                25 // Squeeze bonus
            ];
            
            // Add strong condition bonuses
            const strongBonuses = strongBuyConditions.reduce((sum, condition) => 
                sum + (condition ? 15 : 0), 0
            );
            
            confidence = this.calculateConfidence(baseFactors) + strongBonuses;
            
            if (strongBuyConditions.filter(Boolean).length >= 2) {
                signalStrength = 'STRONG';
            }
            
        } else if (priceBelowEma && squeezeDarkBlue && rsiBelow50) {
            signal = {
                type: 'SELL',
                timestamp,
                index,
                price: currentPrice,
                ema,
                rsi: currentRsi,
                squeeze: currentSqueeze,
                conditions: {
                    priceVsEma: ((currentPrice - ema) / ema * 100).toFixed(2) + '%',
                    rsi: currentRsi.toFixed(1),
                    squeeze: currentSqueeze,
                    momentum: this.getMomentumDescription(index)
                }
            };
            
            // Calculate confidence
            const baseFactors = [
                (ema - currentPrice) / ema * 100, // Price discount
                50 - currentRsi, // RSI weakness
                25 // Squeeze bonus
            ];
            
            // Add strong condition bonuses
            const strongBonuses = strongSellConditions.reduce((sum, condition) => 
                sum + (condition ? 15 : 0), 0
            );
            
            confidence = this.calculateConfidence(baseFactors) + strongBonuses;
            
            if (strongSellConditions.filter(Boolean).length >= 2) {
                signalStrength = 'STRONG';
            }
        }
        
        if (signal) {
            signal.confidence = Math.min(100, Math.max(0, confidence));
            signal.strength = signalStrength;
            signal.id = this.generateSignalId();
        }
        
        return signal;
    }
    
    shouldEmitSignal(signal, currentIndex) {
        // Prevent duplicate signals of the same type
        if (this.lastSignal && 
            this.lastSignal.type === signal.type && 
            (currentIndex - this.lastSignal.index) < this.minSignalDistance) {
            return false;
        }
        
        // Confidence threshold
        if (signal.confidence < 30) {
            return false;
        }
        
        return true;
    }
    
    checkMomentum(index, direction) {
        // Simple momentum check - would need access to price history
        // This is a placeholder for more sophisticated momentum analysis
        return Math.random() > 0.6; // 40% chance of strong momentum
    }
    
    getMomentumDescription(index) {
        const momentumTypes = ['Weak', 'Moderate', 'Strong'];
        return momentumTypes[Math.floor(Math.random() * momentumTypes.length)];
    }
    
    calculateConfidence(factors) {
        const weighted = factors.reduce((sum, factor, index) => {
            const weights = [3, 2, 1]; // Price vs EMA is most important
            return sum + (Math.abs(factor) * (weights[index] || 1));
        }, 0);
        
        return Math.min(80, weighted); // Cap at 80% for base confidence
    }
    
    generateSignalId() {
        return `signal_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    getSignalHistory(limit = 50) {
        return this.signalBuffer.slice(-limit);
    }
    
    clearHistory() {
        this.signalBuffer = [];
    }
}

module.exports = SignalGenerator;

// src/websocket/WebSocketManager.js
const WebSocket = require('ws');
const EventEmitter = require('events');

class WebSocketManager extends EventEmitter {
    constructor(server, tradingBot) {
        super();
        this.server = server;
        this.tradingBot = tradingBot;
        this.clients = new Set();
        this.wss = null;
        
        this.initialize();
        this.setupTradingBotListeners();
    }
    
    initialize() {
        this.wss = new WebSocket.Server({ 
            server: this.server,
            path: '/ws'
        });
        
        this.wss.on('connection', (ws, req) => {
            this.handleNewConnection(ws, req);
        });
        
        console.log('🔌 WebSocket server initialized');
    }
    
    handleNewConnection(ws, req) {
        const clientId = this.generateClientId();
        ws.clientId = clientId;
        
        this.clients.add(ws);
        console.log(`👤 Client connected: ${clientId} (Total: ${this.clients.size})`);
        
        // Send initial data
        this.sendToClient(ws, {
            type: 'init',
            data: {
                ...this.tradingBot.getData(),
                status: this.tradingBot.getStatus(),
                clientId
            }
        });
        
        // Setup client event handlers
        ws.on('message', (message) => {
            this.handleClientMessage(ws, message);
        });
        
        ws.on('close