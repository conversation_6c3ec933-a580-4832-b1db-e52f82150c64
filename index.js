// SPX Trading Bot - Professional Implementation
// Author: 15-Year Experienced Algorithm Developer
// Strategy: EMA + Squeeze + RSI Multi-Timeframe Analysis

const express = require('express');
const WebSocket = require('ws');
const axios = require('axios');
const cors = require('cors');
const path = require('path');

class TechnicalIndicators {
    static calculateEMA(prices, period) {
        if (prices.length < period) return [];
        
        const multiplier = 2 / (period + 1);
        const ema = [];
        
        // Start with SMA for the first value
        let sum = 0;
        for (let i = 0; i < period; i++) {
            sum += prices[i];
        }
        ema[period - 1] = sum / period;
        
        // Calculate EMA for subsequent values
        for (let i = period; i < prices.length; i++) {
            ema[i] = (prices[i] * multiplier) + (ema[i - 1] * (1 - multiplier));
        }
        
        return ema;
    }
    
    static calculateRSI(prices, period = 14) {
        if (prices.length < period + 1) return [];
        
        const rsi = [];
        let gains = 0;
        let losses = 0;
        
        // Calculate initial average gain and loss
        for (let i = 1; i <= period; i++) {
            const change = prices[i] - prices[i - 1];
            if (change >= 0) {
                gains += change;
            } else {
                losses -= change;
            }
        }
        
        let avgGain = gains / period;
        let avgLoss = losses / period;
        let rs = avgGain / avgLoss;
        rsi[period] = 100 - (100 / (1 + rs));
        
        // Calculate subsequent RSI values
        for (let i = period + 1; i < prices.length; i++) {
            const change = prices[i] - prices[i - 1];
            const gain = change >= 0 ? change : 0;
            const loss = change < 0 ? -change : 0;
            
            avgGain = (avgGain * (period - 1) + gain) / period;
            avgLoss = (avgLoss * (period - 1) + loss) / period;
            
            rs = avgGain / avgLoss;
            rsi[i] = 100 - (100 / (1 + rs));
        }
        
        return rsi;
    }
    
    static calculateBollingerBands(prices, period = 20, stdDev = 2) {
        if (prices.length < period) return { upper: [], middle: [], lower: [] };
        
        const upper = [];
        const middle = [];
        const lower = [];
        
        for (let i = period - 1; i < prices.length; i++) {
            const slice = prices.slice(i - period + 1, i + 1);
            const sma = slice.reduce((a, b) => a + b) / period;
            
            const variance = slice.reduce((sum, price) => {
                return sum + Math.pow(price - sma, 2);
            }, 0) / period;
            
            const standardDeviation = Math.sqrt(variance);
            
            middle[i] = sma;
            upper[i] = sma + (standardDeviation * stdDev);
            lower[i] = sma - (standardDeviation * stdDev);
        }
        
        return { upper, middle, lower };
    }
    
    static calculateSqueeze(prices, period = 20, keltnerPeriod = 20, keltnerMultiplier = 1.5) {
        const bb = this.calculateBollingerBands(prices, period);
        const ema = this.calculateEMA(prices, keltnerPeriod);
        
        // Calculate Keltner Channels (simplified using price range)
        const keltner = { upper: [], lower: [] };
        
        for (let i = keltnerPeriod - 1; i < prices.length; i++) {
            if (ema[i] !== undefined) {
                // Simplified ATR calculation
                const slice = prices.slice(Math.max(0, i - keltnerPeriod + 1), i + 1);
                const high = Math.max(...slice);
                const low = Math.min(...slice);
                const atr = (high - low) / slice.length;
                
                keltner.upper[i] = ema[i] + (atr * keltnerMultiplier);
                keltner.lower[i] = ema[i] - (atr * keltnerMultiplier);
            }
        }
        
        // Determine squeeze state
        const squeeze = [];
        for (let i = 0; i < prices.length; i++) {
            if (bb.upper[i] !== undefined && keltner.upper[i] !== undefined) {
                if (bb.upper[i] < keltner.upper[i] && bb.lower[i] > keltner.lower[i]) {
                    squeeze[i] = 'SQUEEZE'; // Dark Blue - High Compression
                } else if (bb.upper[i] <= keltner.upper[i] * 1.1 && bb.lower[i] >= keltner.lower[i] * 0.9) {
                    squeeze[i] = 'YELLOW'; // Yellow - Medium Compression
                } else {
                    squeeze[i] = 'GREEN'; // Green - No Squeeze
                }
            }
        }
        
        return squeeze;
    }
}

class SPXTradingBot {
    constructor() {
        this.app = express();
        this.server = null;
        this.wss = null;
        this.clients = new Set();
        this.marketData = {
            prices: [],
            timestamps: [],
            signals: [],
            indicators: {}
        };
        this.config = {
            symbol: 'SPX',
            timeframes: ['1min', '5min', '15min', '1h', '4h', '1d'],
            currentTimeframe: '1d',
            emaLength: 50,
            rsiLength: 14,
            updateInterval: 5000, // 5 seconds
            apiKey: process.env.ALPHA_VANTAGE_API_KEY || 'demo'
        };
        this.isRunning = false;
        this.setupExpress();
        this.initializeWebSocket();
    }
    
    setupExpress() {
        this.app.use(cors());
        this.app.use(express.json());
        this.app.use(express.static('public'));
        
        // API Routes
        this.app.get('/api/status', (req, res) => {
            res.json({
                status: this.isRunning ? 'running' : 'stopped',
                symbol: this.config.symbol,
                timeframe: this.config.currentTimeframe,
                lastUpdate: new Date().toISOString(),
                totalSignals: this.marketData.signals.length
            });
        });
        
        this.app.post('/api/config', (req, res) => {
            const { timeframe, symbol } = req.body;
            if (timeframe && this.config.timeframes.includes(timeframe)) {
                this.config.currentTimeframe = timeframe;
            }
            if (symbol) {
                this.config.symbol = symbol;
            }
            res.json({ success: true, config: this.config });
        });
        
        this.app.post('/api/start', (req, res) => {
            this.start();
            res.json({ success: true, message: 'Bot started' });
        });
        
        this.app.post('/api/stop', (req, res) => {
            this.stop();
            res.json({ success: true, message: 'Bot stopped' });
        });
        
        this.app.get('/api/data', (req, res) => {
            res.json({
                marketData: this.marketData,
                config: this.config
            });
        });
    }
    
    initializeWebSocket() {
        this.server = require('http').createServer(this.app);
        this.wss = new WebSocket.Server({ server: this.server });
        
        this.wss.on('connection', (ws) => {
            this.clients.add(ws);
            console.log('Client connected. Total clients:', this.clients.size);
            
            // Send initial data
            ws.send(JSON.stringify({
                type: 'init',
                data: {
                    marketData: this.marketData,
                    config: this.config,
                    status: this.isRunning
                }
            }));
            
            ws.on('close', () => {
                this.clients.delete(ws);
                console.log('Client disconnected. Total clients:', this.clients.size);
            });
            
            ws.on('message', (message) => {
                try {
                    const data = JSON.parse(message);
                    this.handleWebSocketMessage(data, ws);
                } catch (error) {
                    console.error('WebSocket message error:', error);
                }
            });
        });
    }
    
    handleWebSocketMessage(data, ws) {
        switch (data.type) {
            case 'changeTimeframe':
                if (this.config.timeframes.includes(data.timeframe)) {
                    this.config.currentTimeframe = data.timeframe;
                    this.fetchMarketData();
                }
                break;
            case 'getSignals':
                ws.send(JSON.stringify({
                    type: 'signals',
                    data: this.marketData.signals
                }));
                break;
        }
    }
    
    broadcast(data) {
        const message = JSON.stringify(data);
        this.clients.forEach(client => {
            if (client.readyState === WebSocket.OPEN) {
                client.send(message);
            }
        });
    }
    
    async fetchMarketData() {
        try {
            // Using Alpha Vantage API for demo (replace with your preferred data source)
            const timeSeriesMap = {
                '1min': 'TIME_SERIES_INTRADAY&interval=1min',
                '5min': 'TIME_SERIES_INTRADAY&interval=5min',
                '15min': 'TIME_SERIES_INTRADAY&interval=15min',
                '1h': 'TIME_SERIES_INTRADAY&interval=60min',
                '4h': 'TIME_SERIES_INTRADAY&interval=60min', // Aggregate 4 hourly bars
                '1d': 'TIME_SERIES_DAILY'
            };
            
            const function_name = timeSeriesMap[this.config.currentTimeframe] || 'TIME_SERIES_DAILY';
            
            // For demo purposes, generate sample data if no API key
            if (this.config.apiKey === 'demo') {
                this.generateSampleData();
                return;
            }
            
            const response = await axios.get(
                `https://www.alphavantage.co/query?function=${function_name}&symbol=${this.config.symbol}&apikey=${this.config.apiKey}&outputsize=full`
            );
            
            this.processMarketData(response.data);
            
        } catch (error) {
            console.error('Error fetching market data:', error);
            // Fallback to sample data
            this.generateSampleData();
        }
    }
    
    generateSampleData() {
        const dataPoints = 200;
        const basePrice = 4500;
        const prices = [];
        const timestamps = [];
        
        let currentPrice = basePrice;
        const now = new Date();
        
        for (let i = dataPoints - 1; i >= 0; i--) {
            // Random walk with slight upward bias
            const change = (Math.random() - 0.48) * 20;
            currentPrice = Math.max(4000, Math.min(5000, currentPrice + change));
            prices.push(currentPrice);
            
            const timestamp = new Date(now.getTime() - (i * this.getTimeframeMs()));
            timestamps.push(timestamp.toISOString());
        }
        
        this.marketData.prices = prices;
        this.marketData.timestamps = timestamps;
        this.calculateIndicators();
        this.generateSignals();
    }
    
    getTimeframeMs() {
        const timeframeMap = {
            '1min': 60 * 1000,
            '5min': 5 * 60 * 1000,
            '15min': 15 * 60 * 1000,
            '1h': 60 * 60 * 1000,
            '4h': 4 * 60 * 60 * 1000,
            '1d': 24 * 60 * 60 * 1000
        };
        return timeframeMap[this.config.currentTimeframe] || timeframeMap['1d'];
    }
    
    processMarketData(data) {
        try {
            const timeSeries = data[Object.keys(data).find(key => key.includes('Time Series'))];
            if (!timeSeries) throw new Error('No time series data found');
            
            const prices = [];
            const timestamps = [];
            
            Object.keys(timeSeries).reverse().forEach(timestamp => {
                const candle = timeSeries[timestamp];
                prices.push(parseFloat(candle['4. close']));
                timestamps.push(timestamp);
            });
            
            this.marketData.prices = prices;
            this.marketData.timestamps = timestamps;
            this.calculateIndicators();
            this.generateSignals();
            
        } catch (error) {
            console.error('Error processing market data:', error);
            this.generateSampleData();
        }
    }
    
    calculateIndicators() {
        const { prices } = this.marketData;
        
        this.marketData.indicators = {
            ema50: TechnicalIndicators.calculateEMA(prices, this.config.emaLength),
            rsi: TechnicalIndicators.calculateRSI(prices, this.config.rsiLength),
            squeeze: TechnicalIndicators.calculateSqueeze(prices),
            bollingerBands: TechnicalIndicators.calculateBollingerBands(prices)
        };
    }
    
    generateSignals() {
        const { prices, timestamps, indicators } = this.marketData;
        const { ema50, rsi, squeeze } = indicators;
        const signals = [];
        
        for (let i = 50; i < prices.length; i++) { // Start after EMA warmup
            const currentPrice = prices[i];
            const ema = ema50[i];
            const currentRsi = rsi[i];
            const currentSqueeze = squeeze[i];
            
            if (!ema || !currentRsi || !currentSqueeze) continue;
            
            // Buy Signal Logic
            const priceAboveEma = currentPrice > (ema * 1.01); // 1% above EMA
            const squeezeYellow = currentSqueeze === 'YELLOW';
            const rsiAbove50 = currentRsi > 50;
            
            // Sell Signal Logic
            const priceBelowEma = currentPrice < (ema * 0.99); // 1% below EMA
            const squeezeDarkBlue = currentSqueeze === 'SQUEEZE'; // Dark blue
            const rsiBelow50 = currentRsi < 50;
            
            let signal = null;
            let confidence = 0;
            
            if (priceAboveEma && squeezeYellow && rsiAbove50) {
                signal = 'BUY';
                confidence = this.calculateConfidence([
                    (currentPrice - ema) / ema * 100, // Price premium over EMA
                    currentRsi - 50, // RSI strength above midline
                    squeezeYellow ? 25 : 0 // Squeeze condition bonus
                ]);
            } else if (priceBelowEma && squeezeDarkBlue && rsiBelow50) {
                signal = 'SELL';
                confidence = this.calculateConfidence([
                    (ema - currentPrice) / ema * 100, // Price discount under EMA
                    50 - currentRsi, // RSI weakness below midline
                    squeezeDarkBlue ? 25 : 0 // Squeeze condition bonus
                ]);
            }
            
            if (signal) {
                // Avoid duplicate signals
                const lastSignal = signals[signals.length - 1];
                if (!lastSignal || lastSignal.type !== signal || i - lastSignal.index > 5) {
                    signals.push({
                        type: signal,
                        timestamp: timestamps[i],
                        index: i,
                        price: currentPrice,
                        ema: ema,
                        rsi: currentRsi,
                        squeeze: currentSqueeze,
                        confidence: Math.min(100, Math.max(0, confidence)),
                        conditions: {
                            priceVsEma: ((currentPrice - ema) / ema * 100).toFixed(2) + '%',
                            rsi: currentRsi.toFixed(1),
                            squeeze: currentSqueeze
                        }
                    });
                }
            }
        }
        
        this.marketData.signals = signals;
        console.log(`Generated ${signals.length} signals for ${this.config.symbol} ${this.config.currentTimeframe}`);
    }
    
    calculateConfidence(factors) {
        // Weight and normalize factors
        const weighted = factors.reduce((sum, factor, index) => {
            const weights = [2, 1.5, 1]; // Price vs EMA is most important
            return sum + (Math.abs(factor) * (weights[index] || 1));
        }, 0);
        
        return Math.min(100, weighted);
    }
    
    async start() {
        if (this.isRunning) return;
        
        this.isRunning = true;
        console.log(`Starting SPX Trading Bot - ${this.config.symbol} ${this.config.currentTimeframe}`);
        
        // Initial data fetch
        await this.fetchMarketData();
        
        // Set up periodic updates
        this.updateInterval = setInterval(() => {
            if (this.isRunning) {
                this.fetchMarketData();
                this.broadcast({
                    type: 'update',
                    data: {
                        marketData: this.marketData,
                        timestamp: new Date().toISOString()
                    }
                });
            }
        }, this.config.updateInterval);
        
        this.broadcast({
            type: 'status',
            data: { running: true, message: 'Bot started successfully' }
        });
    }
    
    stop() {
        this.isRunning = false;
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
        
        console.log('SPX Trading Bot stopped');
        this.broadcast({
            type: 'status',
            data: { running: false, message: 'Bot stopped' }
        });
    }
    
    listen(port = 3000) {
        this.server.listen(port, () => {
            console.log(`SPX Trading Bot Server running on port ${port}`);
            console.log(`Dashboard: http://localhost:${port}`);
        });
    }
}

// HTML Dashboard (served as static file)
const dashboardHTML = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SPX Trading Bot Dashboard</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://s3.tradingview.com/external-embedding/embed-widget-advanced-chart.js"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #0a0a0a;
            color: #ffffff;
            line-height: 1.6;
        }
        .container { max-width: 1400px; margin: 0 auto; padding: 20px; }
        .header { 
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
        }
        .title { 
            font-size: 2.5rem; 
            font-weight: bold; 
            background: linear-gradient(45deg, #00ff88, #0099ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .status { 
            display: flex; 
            gap: 10px; 
            align-items: center; 
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #ff4444;
            animation: pulse 2s infinite;
        }
        .status-indicator.running { background: #00ff88; }
        @keyframes pulse {
            0% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.1); }
            100% { opacity: 1; transform: scale(1); }
        }
        .controls {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            padding: 20px;
            background: #1a1a1a;
            border-radius: 10px;
            flex-wrap: wrap;
        }
        .control-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        .control-group label {
            font-size: 0.9rem;
            color: #cccccc;
            font-weight: 500;
        }
        select, button {
            padding: 10px 15px;
            border: none;
            border-radius: 5px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        select {
            background: #2d2d2d;
            color: #ffffff;
            border: 1px solid #444;
        }
        select:focus {
            outline: none;
            border-color: #0099ff;
            box-shadow: 0 0 5px rgba(0,153,255,0.3);
        }
        .btn-primary {
            background: linear-gradient(45deg, #0099ff, #00ff88);
            color: white;
            font-weight: 600;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,153,255,0.3);
        }
        .btn-secondary {
            background: #444;
            color: white;
        }
        .btn-secondary:hover {
            background: #555;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        .panel {
            background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            border: 1px solid #333;
        }
        .panel h3 {
            font-size: 1.3rem;
            margin-bottom: 15px;
            color: #00ff88;
        }
        .chart-container {
            position: relative;
            height: 400px;
            margin-bottom: 20px;
        }
        .tradingview-widget-container {
            height: 500px;
            background: #161623;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 20px;
        }
        .signals-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #333;
            border-radius: 8px;
            background: #0a0a0a;
        }
        .signal-item {
            padding: 15px;
            border-bottom: 1px solid #333;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background 0.3s ease;
        }
        .signal-item:hover {
            background: #1a1a1a;
        }
        .signal-item:last-child {
            border-bottom: none;
        }
        .signal-buy {
            border-left: 4px solid #00ff88;
        }
        .signal-sell {
            border-left: 4px solid #ff4444;
        }
        .signal-type {
            font-weight: bold;
            font-size: 1.1rem;
        }
        .signal-buy .signal-type {
            color: #00ff88;
        }
        .signal-sell .signal-type {
            color: #ff4444;
        }
        .signal-details {
            font-size: 0.9rem;
            color: #cccccc;
        }
        .confidence-bar {
            width: 60px;
            height: 6px;
            background: #333;
            border-radius: 3px;
            overflow: hidden;
        }
        .confidence-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff4444, #ffaa00, #00ff88);
            transition: width 0.3s ease;
        }
        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .metric {
            text-align: center;
            padding: 20px;
            background: #0a0a0a;
            border-radius: 8px;
            border: 1px solid #333;
        }
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            color: #00ff88;
        }
        .metric-label {
            color: #cccccc;
            margin-top: 5px;
        }
        .full-width {
            grid-column: 1 / -1;
        }
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
            .controls {
                flex-direction: column;
            }
            .header {
                flex-direction: column;
                text-align: center;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">SPX Trading Bot</h1>
            <div class="status">
                <div class="status-indicator" id="statusIndicator"></div>
                <span id="statusText">Disconnected</span>
            </div>
        </div>
        
        <div class="controls">
            <div class="control-group">
                <label>Timeframe</label>
                <select id="timeframeSelect">
                    <option value="1min">1 Minute</option>
                    <option value="5min">5 Minutes</option>
                    <option value="15min">15 Minutes</option>
                    <option value="1h">1 Hour</option>
                    <option value="4h">4 Hours</option>
                    <option value="1d" selected>1 Day</option>
                </select>
            </div>
            <div class="control-group">
                <label>Actions</label>
                <div style="display: flex; gap: 10px;">
                    <button id="startBtn" class="btn-primary">Start Bot</button>
                    <button id="stopBtn" class="btn-secondary">Stop Bot</button>
                </div>
            </div>
        </div>
        
        <div class="panel full-width">
            <h3>TradingView Chart</h3>
            <div class="tradingview-widget-container" id="tradingview_chart">
                <div class="tradingview-widget-copyright">
                    <a href="https://www.tradingview.com/symbols/SPX/" rel="noopener" target="_blank">
                        <span class="blue-text">SPX Chart</span>
                    </a> by TradingView
                </div>
            </div>
        </div>
        
        <div class="grid">
            <div class="panel">
                <h3>Price Chart & Indicators</h3>
                <div class="chart-container">
                    <canvas id="priceChart"></canvas>
                </div>
            </div>
            
            <div class="panel">
                <h3>Recent Signals</h3>
                <div class="signals-list" id="signalsList">
                    <div style="text-align: center; padding: 50px; color: #666;">
                        No signals yet...
                    </div>
                </div>
            </div>
        </div>
        
        <div class="panel">
            <h3>Performance Metrics</h3>
            <div class="metrics">
                <div class="metric">
                    <div class="metric-value" id="totalSignals">0</div>
                    <div class="metric-label">Total Signals</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="buySignals">0</div>
                    <div class="metric-label">Buy Signals</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="sellSignals">0</div>
                    <div class="metric-label">Sell Signals</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="avgConfidence">0%</div>
                    <div class="metric-label">Avg Confidence</div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        class TradingBotDashboard {
            constructor() {
                this.ws = null;
                this.priceChart = null;
                this.initializeChart();
                this.setupEventListeners();
                this.connectWebSocket();
                this.initializeTradingView();
            }
            
            connectWebSocket() {
                const protocol =